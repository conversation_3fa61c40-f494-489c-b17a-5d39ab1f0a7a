"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConditionalTypeScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class ConditionalTypeScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON>ana<PERSON>, ScopeType_1.ScopeType.conditionalType, upperScope, block, false);
    }
}
exports.ConditionalTypeScope = ConditionalTypeScope;
