"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2020_symbol_wellknown = void 0;
const base_config_1 = require("./base-config");
const es2015_iterable_1 = require("./es2015.iterable");
const es2015_symbol_1 = require("./es2015.symbol");
exports.es2020_symbol_wellknown = {
    libs: [es2015_iterable_1.es2015_iterable, es2015_symbol_1.es2015_symbol],
    variables: [
        ['SymbolConstructor', base_config_1.TYPE],
        ['RegExpStringIterator', base_config_1.TYPE],
        ['RegExp', base_config_1.TYPE],
    ],
};
